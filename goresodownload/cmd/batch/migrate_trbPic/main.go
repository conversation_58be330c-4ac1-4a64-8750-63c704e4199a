/*
###
Description:    Batch migrate downloaded property images to new directory structure

Usage:         ./start.sh -n migrate_trbPic -d "goresodownload" -cmd "cmd/batch/migrate_trbPic/main.go -board=TRB -dir=/1200 -dryrun"

Create date:    2025-07-28
Author:         Assistant
Run frequency:  As needed
###
*/
package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	goconfig "github.com/real-rm/goconfig"
	levelStore "github.com/real-rm/golevelstore"
	golog "github.com/real-rm/golog"
	gomongo "github.com/real-rm/gomongo"
	"github.com/real-rm/goresodownload"
	gospeedmeter "github.com/real-rm/gospeedmeter"
	gostreaming "github.com/real-rm/gostreaming"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

var (
	dryrunFlag = flag.Bool("dryrun", false, "Dry run mode - only log operations without executing them")
	boardFlag  = flag.String("board", "TRB", "Board name for file path generation")
	dirFlag    = flag.String("dir", "", "Target directory to migrate (e.g., /1200/abc12)")
	speedMeter *gospeedmeter.SpeedMeter
	dirStore   *levelStore.DirKeyStore
	queue      *goresodownload.ResourceDownloadQueue
	startTime  = time.Now()
)

// MigrationStats holds statistics for the migration process
type MigrationStats struct {
	TotalProcessed   int64
	NeedsMigration   int64
	AlreadyCorrect   int64
	MigrationSuccess int64
	MigrationFailed  int64
	DatabaseUpdated  int64
	DatabaseFailed   int64
	FilesNotFound    int64
	FilesDeleted     int64
	AddedToQueue     int64
	FieldsUnset      int64
}

// DocInfo holds document file information with hash and extension
type DocInfo struct {
	Hash      int32  // The int32 hash derived from filename
	Extension string // The file extension (e.g., ".pdf", ".mp3")
}

// FileItem represents a file to be migrated with its metadata
type FileItem struct {
	Hash      string // The base62 hash for the file
	Extension string // The file extension (e.g., ".jpg", ".pdf", ".mp3")
	Type      string // File type for logging ("image" or "document")
}

// FilenameGenerator defines how to generate filenames for different file types
type FilenameGenerator func(listingKey, hash, extension string) string

// PathExtractor defines how to extract file path from migration result
type PathExtractor func(result MigrationResult) string

// setting up logging, and establishing MongoDB connection.
func init() {
	// Load configuration
	if err := goconfig.LoadConfig(); err != nil {
		golog.Fatalf("Failed to load config: %v", err)
	}

	// Initialize logging first
	if err := golog.InitLog(); err != nil {
		golog.Fatalf("Failed to initialize logging: %v", err)
	}

	// Initialize MongoDB last (after config is loaded)
	if err := gomongo.InitMongoDB(); err != nil {
		golog.Fatalf("Failed to initialize MongoDB: %v", err)
	}

	// Initialize speed meter
	speedMeter = gospeedmeter.NewSpeedMeter(gospeedmeter.SpeedMeterOptions{})

	// Initialize dirStore for directory statistics
	collection := gomongo.Coll("rni", "file_server")
	var err error
	dirStore, err = levelStore.NewDirKeyStore(*boardFlag, collection, "")
	if err != nil {
		golog.Fatalf("Failed to create dirKeyStore: %v", err)
	}
	if dirStore == nil {
		golog.Fatalf("dirKeyStore is nil")
	}

	// Initialize download queue
	queueCol := gomongo.Coll("rni", "reso_photo_download_queue")
	queue, err = goresodownload.NewResourceDownloadQueue(queueCol)
	if err != nil {
		golog.Fatalf("Failed to create download queue: %v", err)
	}
}

func main() {
	// Create context with timeout to prevent hanging
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	golog.Info("Starting migrate_trbPic batch process")
	if err := processProperties(ctx); err != nil {
		golog.Fatal("Failed to process properties", "error", err)
	}
	golog.Info("migrate_trbPic batch process completed successfully")
}

func processProperties(ctx context.Context) error {
	// Parse command line flags
	flag.Parse()

	// Validate required parameters
	if *dirFlag == "" {
		return fmt.Errorf("dir parameter is required (e.g., -dir=/1200/abc12)")
	}

	// Normalize directory path (ensure it starts with /)
	if !strings.HasPrefix(*dirFlag, "/") {
		*dirFlag = "/" + *dirFlag
	}

	// Determine query type based on directory format
	var queryType string
	if strings.HasSuffix(*dirFlag, "/") || strings.Count(*dirFlag, "/") == 1 {
		queryType = "prefix match (regex)"
	} else {
		queryType = "exact match"
	}

	golog.Info("Starting migrate_trbPic batch processing",
		"dryrun", *dryrunFlag,
		"board", *boardFlag,
		"dir", *dirFlag,
		"queryType", queryType)

	// Validate board flag
	collectionName, exists := goresodownload.BoardMergedTable[*boardFlag]
	if !exists {
		return fmt.Errorf("invalid board: %s. Valid boards: CAR, DDF, BRE, EDM, TRB", *boardFlag)
	}

	// Get storage paths for file operations
	storagePaths, err := levelStore.GetImageDir(*boardFlag)
	if err != nil {
		return fmt.Errorf("failed to get image directories: %w", err)
	}
	if len(storagePaths) == 0 {
		return fmt.Errorf("no image directories configured for board %s", *boardFlag)
	}

	// Get collection using BoardMergedTable
	coll := gomongo.Coll("rni", collectionName)
	golog.Info("Processing collection",
		"board", *boardFlag,
		"collection", collectionName,
		"dryrun", *dryrunFlag,
		"storagePaths", storagePaths)

	// Query documents with the specified phoP directory
	// Support both exact match and prefix match (regex)
	var query bson.M
	if strings.HasSuffix(*dirFlag, "/") || strings.Count(*dirFlag, "/") == 1 {
		// If directory ends with "/" or only has one "/" (L1 level), use regex for prefix match
		// Example: "/1200" should match "/1200/abc12", "/1200/def34", etc.
		regexPattern := "^" + strings.TrimSuffix(*dirFlag, "/") + "/"
		query = bson.M{
			"phoP": bson.M{"$regex": regexPattern},
		}
		golog.Info("Using regex pattern for directory matching", "pattern", regexPattern)
	} else {
		// Exact match for full paths like "/1200/abc12"
		query = bson.M{
			"phoP": *dirFlag,
		}
		golog.Info("Using exact match for directory", "dir", *dirFlag)
	}

	// 根据board类型确定需要查询的字段
	projection := bson.D{
		{Key: "_id", Value: 1},
		{Key: "ts", Value: 1},
		{Key: "ListingKey", Value: 1},
		{Key: "ListingId", Value: 1},
		{Key: "phoP", Value: 1},
		{Key: "phoLH", Value: 1},
		{Key: "tnLH", Value: 1},
		{Key: "docLH", Value: 1},
		{Key: "ListingContractDate", Value: 1},
		{Key: "ModificationTimestamp", Value: 1},
		{Key: "OriginalEntryTimestamp", Value: 1},
		{Key: "BridgeModificationTimestamp", Value: 1},
		// CalculatePriority 需要的字段
		{Key: "MlsStatus", Value: 1},
		{Key: "CountyOrParish", Value: 1},
		{Key: "PropertyType", Value: 1},
		{Key: "StateOrProvince", Value: 1},
		{Key: "DaysOnMarket", Value: 1},
	}

	options := gomongo.QueryOptions{
		Projection: projection,
	}

	// Get cursor
	golog.Info("Executing query", "query", query)
	cursor, err := coll.Find(ctx, query, options)
	if err != nil {
		golog.Error("Failed to execute query", "error", err)
		return fmt.Errorf("failed to execute query: %v", err)
	}
	golog.Info("Query executed successfully, starting streaming")

	// Initialize migration stats
	stats := &MigrationStats{}

	opts := gostreaming.StreamingOptions{
		Stream: cursor,
		Process: func(item interface{}) error {
			return processItem(ctx, coll, item, storagePaths, stats)
		},
		End: func(err error) {
			duration := time.Since(startTime)
			printFinalStats(stats, duration)
			if err != nil {
				golog.Error("Stream ended with error", "error", err, "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
			} else {
				golog.Info("Stream completed successfully", "speed", speedMeter.ToString(gospeedmeter.UnitM, nil))
			}
		},
		Error: func(err error) {
			golog.Error("Processing error", "error", err)
		},
		High:    10,
		Verbose: 2,
	}

	golog.Info("Starting gostreaming.Streaming")
	err = gostreaming.Streaming(ctx, &opts)
	if err != nil {
		golog.Error("Failed to stream data", "error", err)
		return err
	}
	golog.Info("Streaming completed successfully")
	return nil
}

// processItem processes a single document to migrate images if needed
func processItem(ctx context.Context, coll *gomongo.MongoCollection, item interface{}, storagePaths []string, stats *MigrationStats) error {
	golog.Debug("Processing item started")

	// Track processing speed
	speedMeter.Check("processed", 1)
	stats.TotalProcessed++

	// Convert item to bson.M (handle both bson.M and bson.D types)
	var doc bson.M
	switch v := item.(type) {
	case bson.M:
		doc = v
	case bson.D:
		// Convert bson.D to bson.M using marshal/unmarshal
		data, err := bson.Marshal(v)
		if err != nil {
			golog.Error("Failed to marshal bson.D", "error", err, "item", item)
			speedMeter.Check("errors", 1)
			return fmt.Errorf("failed to marshal bson.D: %v", err)
		}
		if err := bson.Unmarshal(data, &doc); err != nil {
			golog.Error("Failed to unmarshal to bson.M", "error", err, "item", item)
			speedMeter.Check("errors", 1)
			return fmt.Errorf("failed to unmarshal to bson.M: %v", err)
		}
	default:
		golog.Error("Unsupported document type", "type", fmt.Sprintf("%T", item), "item", item)
		speedMeter.Check("errors", 1)
		return fmt.Errorf("unsupported document type: %T", item)
	}

	golog.Debug("Item converted to bson.M successfully")

	// Extract required fields
	id, ok := doc["_id"].(string)
	if !ok {
		golog.Error("Failed to extract _id", "doc", doc)
		speedMeter.Check("idErrors", 1)
		return fmt.Errorf("failed to extract _id")
	}

	// 根据board类型确定使用哪个字段作为listingKey
	listingKey, err := getListingKey(doc, *boardFlag)
	if err != nil {
		golog.Error("Failed to get listing key", "error", err, "_id", id)
		speedMeter.Check("listingKeyErrors", 1)
		return err
	}

	currentPhoP, ok := doc["phoP"].(string)
	if !ok {
		golog.Error("Failed to extract phoP", "doc", doc, "_id", id)
		speedMeter.Check("phoPErrors", 1)
		return fmt.Errorf("failed to extract phoP")
	}

	// 使用新的PropTs获取逻辑
	ts, err := goresodownload.GetPropTsForPath(doc, *boardFlag)
	if err != nil {
		golog.Error("Failed to get PropTs for path",
			"_id", id, "board", *boardFlag, "error", err)
		speedMeter.Check("tsErrors", 1)
		return fmt.Errorf("failed to get PropTs for path: %w", err)
	}

	// Get new file path using levelStore
	newPhoP, err := levelStore.GetFullFilePathForProp(ts, *boardFlag, listingKey)
	if err != nil {
		golog.Error("Failed to get full file path",
			"_id", id,
			"listingKey", listingKey,
			"ts", ts,
			"error", err)
		speedMeter.Check("filePathErrors", 1)
		return fmt.Errorf("failed to get full file path: %w", err)
	}

	// Check if migration is needed
	if currentPhoP == newPhoP {
		golog.Debug("Path already correct, no migration needed",
			"_id", id,
			"path", currentPhoP)
		stats.AlreadyCorrect++
		speedMeter.Check("alreadyCorrect", 1)
		return nil
	}

	golog.Info("Migration needed",
		"_id", id,
		"listingKey", listingKey,
		"currentPath", currentPhoP,
		"newPath", newPhoP)
	stats.NeedsMigration++

	// Check if this is a dry run
	if *dryrunFlag {
		speedMeter.Check("dryrun", 1)
		golog.Info("Dry run mode: Would migrate images",
			"_id", id,
			"listingKey", listingKey,
			"from", currentPhoP,
			"to", newPhoP)
		return nil
	}

	// Perform actual migration
	migrationResults, err := migrateImages(ctx, coll, doc, currentPhoP, newPhoP, storagePaths, stats)
	if err != nil {
		golog.Error("Failed to migrate images",
			"_id", id,
			"from", currentPhoP,
			"to", newPhoP,
			"error", err)
		stats.MigrationFailed++
		speedMeter.Check("migrationFailed", 1)
		// 图片迁移失败时不更新数据库，直接返回错误
		return fmt.Errorf("image migration failed, database not updated: %w", err)
	}

	// 图片迁移成功，记录成功统计
	stats.MigrationSuccess++
	speedMeter.Check("migrationSuccess", 1)

	// 只有在图片迁移成功时才更新数据库记录
	update := bson.M{
		"$set": bson.M{
			"phoP": newPhoP,
		},
	}

	result, err := coll.UpdateOne(ctx, bson.M{"_id": id}, update)
	if err != nil {
		golog.Error("Failed to update document, rolling back image migration",
			"_id", id,
			"newPath", newPhoP,
			"error", err)

		// 数据库更新失败时，回滚图片迁移（删除硬链接）
		rollbackImageMigration(migrationResults)

		stats.DatabaseFailed++
		speedMeter.Check("updateErrors", 1)
		return fmt.Errorf("failed to update document, image migration rolled back: %w", err)
	}

	// 数据库更新成功后，删除源文件
	if err := deleteSourceFiles(migrationResults, stats); err != nil {
		golog.Warn("Failed to delete source files after successful database update",
			"_id", id,
			"error", err)
		// 不返回错误，因为数据库已经更新成功，迁移基本完成
	}

	// Track successful database updates
	stats.DatabaseUpdated++
	speedMeter.Check("updated", 1)

	golog.Info("Successfully processed document",
		"_id", id,
		"listingKey", listingKey,
		"from", currentPhoP,
		"to", newPhoP,
		"migrationSuccess", true,
		"modifiedCount", result.ModifiedCount)

	return nil
}

// MigrationResult tracks the result of migrating a single file using hard link strategy
type MigrationResult struct {
	OldPath string
	NewPath string
	Success bool
}

// migrateFile 处理单个文件的迁移（创建硬链接）
// 返回迁移结果，包含成功状态和路径信息
func migrateFile(oldFilePath, newFilePath string) MigrationResult {
	result := MigrationResult{
		OldPath: oldFilePath,
		NewPath: newFilePath,
		Success: false,
	}

	// 检查源文件是否存在
	if _, err := os.Stat(oldFilePath); os.IsNotExist(err) {
		golog.Error("Source file not found during migration",
			"file", oldFilePath)
		return result
	}

	// 检查目标文件是否已存在
	if _, err := os.Stat(newFilePath); err == nil {
		golog.Debug("Destination file already exists, treating as successful (source file will be deleted after database update)", "file", newFilePath)
		result.Success = true
		return result
	}

	// 创建硬链接
	if err := os.Link(oldFilePath, newFilePath); err != nil {
		golog.Error("Failed to create hard link",
			"from", oldFilePath,
			"to", newFilePath,
			"error", err)
		return result
	}

	// 验证硬链接是否成功创建
	if _, err := os.Stat(newFilePath); err != nil {
		// 硬链接创建失败，清理
		if removeErr := os.Remove(newFilePath); removeErr != nil {
			golog.Error("Failed to cleanup failed hard link",
				"hardLink", newFilePath, "cleanupError", removeErr)
		}
		golog.Error("Hard link created but destination file not accessible",
			"file", newFilePath, "error", err)
		return result
	}

	result.Success = true
	golog.Debug("Successfully created hard link (source file will be deleted after database update)", "from", oldFilePath, "to", newFilePath)
	return result
}

// deleteFilesByPattern 批量删除匹配模式的文件
// 使用通配符模式批量删除文件，提高删除效率
func deleteFilesByPattern(dirPath, pattern string) (int, error) {
	fullPattern := filepath.Join(dirPath, pattern)
	matches, err := filepath.Glob(fullPattern)
	if err != nil {
		return 0, fmt.Errorf("failed to glob pattern %s: %w", fullPattern, err)
	}

	if len(matches) == 0 {
		golog.Debug("No files found matching pattern", "pattern", fullPattern)
		return 0, nil
	}

	deletedCount := 0
	var errors []string

	for _, filePath := range matches {
		if err := os.Remove(filePath); err != nil {
			golog.Error("Failed to delete file", "file", filePath, "error", err)
			errors = append(errors, fmt.Sprintf("failed to delete %s: %v", filePath, err))
		} else {
			deletedCount++
			golog.Debug("Successfully deleted file", "file", filePath)
		}
	}

	golog.Info("Batch file deletion completed",
		"pattern", fullPattern,
		"totalMatches", len(matches),
		"deletedSuccess", deletedCount,
		"deleteErrors", len(errors))

	if len(errors) > 0 {
		return deletedCount, fmt.Errorf("batch deletion errors: %s", strings.Join(errors, "; "))
	}

	return deletedCount, nil
}

// migrateFileItems migrates a list of files using the provided filename generator
// This function abstracts the common migration logic for both images and documents
func migrateFileItems(ctx context.Context, coll *gomongo.MongoCollection, doc bson.M,
	fileItems []FileItem, listingKey, oldDir, newDir string,
	filenameGen FilenameGenerator, stats *MigrationStats) ([]MigrationResult, error) {

	var results []MigrationResult

	for _, item := range fileItems {
		filename := filenameGen(listingKey, item.Hash, item.Extension)
		oldFilePath := filepath.Join(oldDir, filename)
		newFilePath := filepath.Join(newDir, filename)

		result := migrateFile(oldFilePath, newFilePath)

		// Handle source file not found
		if !result.Success {
			if _, err := os.Stat(oldFilePath); os.IsNotExist(err) {
				stats.FilesNotFound++
				speedMeter.Check("filesNotFound", 1)
				golog.Error("Source file not found - database inconsistency detected",
					"file", oldFilePath, "listingKey", listingKey,
					"hash", item.Hash, "type", item.Type)

				if err := handleMissingFiles(ctx, coll, doc, stats); err != nil {
					golog.Error("Failed to handle missing files", "error", err, "listingKey", listingKey)
				}

				results = append(results, result)
				// 完成文件/字段清理并加入下载队列,应该继续执行迁移
				return results, nil
			} else {
				results = append(results, result)
				return results, fmt.Errorf("failed to migrate %s file: %s", item.Type, oldFilePath)
			}
		}

		results = append(results, result)
	}

	return results, nil
}

// batchDeleteFiles performs batch deletion of files using pattern matching
// This function abstracts the common batch deletion logic used in multiple places
func batchDeleteFiles(migrationResults []MigrationResult, pathExtractor PathExtractor,
	operationName string, stats *MigrationStats) error {

	if len(migrationResults) == 0 {
		return nil
	}

	golog.Info("Starting batch file deletion",
		"operation", operationName, "fileCount", len(migrationResults))

	// Group files by directory and listingKey
	dirListingKeyMap := make(map[string]map[string]bool) // dir -> listingKey -> exists

	for _, result := range migrationResults {
		if !result.Success {
			continue // Skip failed migrations
		}

		// Extract directory and listingKey from file path
		filePath := pathExtractor(result)
		dir := filepath.Dir(filePath)
		filename := filepath.Base(filePath)

		// Extract listingKey (filename format: listingKey_hash.extension)
		parts := strings.Split(filename, "_")
		if len(parts) >= 2 {
			listingKey := parts[0]

			if dirListingKeyMap[dir] == nil {
				dirListingKeyMap[dir] = make(map[string]bool)
			}
			dirListingKeyMap[dir][listingKey] = true
		}
	}

	totalDeleted := 0
	var errors []string

	// Perform batch deletion for each directory and listingKey
	for dir, listingKeys := range dirListingKeyMap {
		for listingKey := range listingKeys {
			pattern := fmt.Sprintf("%s_*", listingKey)
			deletedCount, err := deleteFilesByPattern(dir, pattern)
			if err != nil {
				golog.Warn("Batch deletion encountered errors",
					"error", err, "dir", dir, "pattern", pattern, "operation", operationName)
				errors = append(errors, fmt.Sprintf("batch deletion in %s with pattern %s: %v", dir, pattern, err))
				// Continue processing other directories
			}

			totalDeleted += deletedCount
			golog.Info("Batch deleted files",
				"operation", operationName,
				"dir", dir,
				"listingKey", listingKey,
				"pattern", pattern,
				"deletedCount", deletedCount)
		}
	}

	// Update statistics if provided
	if stats != nil && totalDeleted > 0 {
		stats.FilesDeleted += int64(totalDeleted)
		speedMeter.Check("filesDeleted", float64(totalDeleted))
	}

	golog.Info("Batch file deletion completed",
		"operation", operationName,
		"totalDirectories", len(dirListingKeyMap),
		"totalDeleted", totalDeleted,
		"errorCount", len(errors),
		"method", "batch pattern matching")

	if len(errors) > 0 {
		return fmt.Errorf("batch %s errors: %s", operationName, strings.Join(errors, "; "))
	}

	return nil
}

// processArrayField processes different MongoDB array types with a unified approach
// This function abstracts the common array processing logic used in multiple places
func processArrayField[T any](arrayField interface{}, fieldName string,
	converter func(interface{}) (T, bool)) ([]T, error) {

	if arrayField == nil {
		return nil, nil
	}

	var results []T

	// Handle different array types from MongoDB
	switch arr := arrayField.(type) {
	case []interface{}:
		for _, item := range arr {
			if converted, ok := converter(item); ok {
				results = append(results, converted)
			} else {
				golog.Warn("Failed to convert array item",
					"field", fieldName, "type", fmt.Sprintf("%T", item), "value", item)
			}
		}
	case primitive.A:
		for _, item := range arr {
			if converted, ok := converter(item); ok {
				results = append(results, converted)
			} else {
				golog.Warn("Failed to convert array item",
					"field", fieldName, "type", fmt.Sprintf("%T", item), "value", item)
			}
		}
	default:
		// Try direct type assertion for native Go slices
		if directResults, ok := arrayField.([]T); ok {
			return directResults, nil
		}
		return nil, fmt.Errorf("%s is not a supported array type, got type: %T", fieldName, arrayField)
	}

	return results, nil
}

// FileHashInfo 包含文件迁移所需的hash信息
type FileHashInfo struct {
	ImageHashes []string  // 图片文件的base62 hash列表
	DocInfos    []DocInfo // 文档文件信息列表
}

// extractFileHashes 从文档中提取所有需要迁移的文件hash信息
// 包括phoLH（图片hash列表）、tnLH（缩略图hash）、docLH（文档hash列表）
func extractFileHashes(doc bson.M) (*FileHashInfo, error) {
	hashInfo := &FileHashInfo{
		ImageHashes: []string{},
		DocInfos:    []DocInfo{},
	}

	// 处理phoLH (photo list hash)
	if phoLH, ok := doc["phoLH"]; ok && phoLH != nil {
		imageHashes, err := processPhotoHashes(phoLH)
		if err != nil {
			return nil, fmt.Errorf("failed to process phoLH: %w", err)
		}
		hashInfo.ImageHashes = append(hashInfo.ImageHashes, imageHashes...)
	}

	// 处理tnLH (thumbnail hash)
	if tnLH, ok := doc["tnLH"]; ok && tnLH != nil {
		if tnHashInt32, ok := tnLH.(int32); ok {
			base62Hash, err := levelStore.Int32ToBase62(tnHashInt32)
			if err != nil {
				golog.Error("Failed to convert tnLH to base62", "hash", tnHashInt32, "error", err)
			} else {
				hashInfo.ImageHashes = append(hashInfo.ImageHashes, base62Hash)
			}
		} else {
			golog.Warn("Expected int32 for tnLH", "type", fmt.Sprintf("%T", tnLH), "value", tnLH)
		}
	}

	// 处理docLH (document list hash)
	if docLH, ok := doc["docLH"]; ok && docLH != nil {
		docInfos, err := processDocLH(docLH)
		if err != nil {
			return nil, fmt.Errorf("failed to process docLH: %w", err)
		}
		hashInfo.DocInfos = docInfos
	}

	return hashInfo, nil
}

// processPhotoHashes 处理图片hash列表，将int32转换为base62字符串
func processPhotoHashes(phoLH interface{}) ([]string, error) {
	// Define converter function for int32 to base62 string
	int32Converter := func(item interface{}) (int32, bool) {
		if hashInt32, ok := item.(int32); ok {
			return hashInt32, true
		}
		return 0, false
	}

	// Use the generic array processor
	int32Hashes, err := processArrayField(phoLH, "phoLH", int32Converter)
	if err != nil {
		return nil, err
	}

	// Convert int32 hashes to base62 strings
	var imageHashes []string
	for _, hashInt32 := range int32Hashes {
		base62Hash, err := levelStore.Int32ToBase62(hashInt32)
		if err != nil {
			golog.Error("Failed to convert hash to base62", "hash", hashInt32, "error", err)
			continue // Skip this hash but continue processing others
		}
		imageHashes = append(imageHashes, base62Hash)
	}

	return imageHashes, nil
}

// migrateFilesInStoragePath 在单个存储路径中迁移所有文件
// 返回迁移结果列表和任何遇到的错误
func migrateFilesInStoragePath(ctx context.Context, coll *gomongo.MongoCollection, doc bson.M,
	hashInfo *FileHashInfo, listingKey, oldPath, newPath, storagePath string, stats *MigrationStats) ([]MigrationResult, error) {

	oldDir := filepath.Join(storagePath, strings.TrimPrefix(oldPath, "/"))
	newDir := filepath.Join(storagePath, strings.TrimPrefix(newPath, "/"))

	// 创建新目录
	if err := os.MkdirAll(newDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create directory %s: %v", newDir, err)
	}

	var allResults []MigrationResult

	// 准备图片文件列表
	var imageItems []FileItem
	for _, hash := range hashInfo.ImageHashes {
		imageItems = append(imageItems, FileItem{
			Hash:      hash,
			Extension: ".jpg",
			Type:      "image",
		})
	}

	// 准备文档文件列表
	var docItems []FileItem
	for _, docInfo := range hashInfo.DocInfos {
		base62Hash, err := levelStore.Int32ToBase62(docInfo.Hash)
		if err != nil {
			golog.Error("Failed to convert document hash to base62", "hash", docInfo.Hash, "error", err)
			return allResults, fmt.Errorf("failed to convert document hash: %d", docInfo.Hash)
		}
		docItems = append(docItems, FileItem{
			Hash:      base62Hash,
			Extension: docInfo.Extension,
			Type:      "document",
		})
	}

	// 定义文件名生成器
	filenameGenerator := func(listingKey, hash, extension string) string {
		return fmt.Sprintf("%s_%s%s", listingKey, hash, extension)
	}

	// 迁移图片文件
	if len(imageItems) > 0 {
		imageResults, err := migrateFileItems(ctx, coll, doc, imageItems, listingKey, oldDir, newDir, filenameGenerator, stats)
		if err != nil {
			allResults = append(allResults, imageResults...)
			return allResults, err
		}
		allResults = append(allResults, imageResults...)
	}

	// 迁移文档文件
	if len(docItems) > 0 {
		docResults, err := migrateFileItems(ctx, coll, doc, docItems, listingKey, oldDir, newDir, filenameGenerator, stats)
		if err != nil {
			allResults = append(allResults, docResults...)
			return allResults, err
		}
		allResults = append(allResults, docResults...)
	}

	return allResults, nil
}

// migrateImages migrates image files from old directory to new directory using hard link strategy
// Creates hard links to new location (source files will be deleted later after database update)
// Returns a list of successfully migrated files for potential rollback and any error encountered
// If files are missing, it will unset photo fields and add property to download queue
func migrateImages(ctx context.Context, coll *gomongo.MongoCollection, doc bson.M, oldPath, newPath string, storagePaths []string, stats *MigrationStats) ([]MigrationResult, error) {
	// 提取所有需要迁移的文件hash信息
	hashInfo, err := extractFileHashes(doc)
	if err != nil {
		return nil, fmt.Errorf("failed to extract file hashes: %w", err)
	}

	// 检查是否有文件需要迁移
	totalFiles := len(hashInfo.ImageHashes) + len(hashInfo.DocInfos)
	if totalFiles == 0 {
		golog.Debug("No files found for migration", "oldPath", oldPath, "newPath", newPath)
		return nil, nil
	}

	// 获取listingKey用于文件名生成
	listingKey, err := getListingKey(doc, *boardFlag)
	if err != nil {
		return nil, fmt.Errorf("failed to get listing key for file migration: %w", err)
	}

	golog.Info("Starting file migration with hard link strategy (source files will be deleted after database update)",
		"oldPath", oldPath,
		"newPath", newPath,
		"imageCount", len(hashInfo.ImageHashes),
		"documentCount", len(hashInfo.DocInfos),
		"totalFiles", totalFiles,
		"listingKey", listingKey)

	// 跟踪所有迁移结果用于回滚
	var allMigrationResults []MigrationResult
	migratedCount := 0

	// 遍历所有存储路径进行文件迁移
	for _, storagePath := range storagePaths {
		// 使用新的函数处理单个存储路径的迁移
		results, err := migrateFilesInStoragePath(ctx, coll, doc, hashInfo, listingKey, oldPath, newPath, storagePath, stats)
		if err != nil {
			golog.Warn("Migration failed for storage path, rolling back successfully migrated files",
				"storagePath", storagePath,
				"error", err,
				"migrationResultsCount", len(allMigrationResults))

			// 回滚已成功迁移的文件
			rollbackImageMigration(allMigrationResults)
			return nil, fmt.Errorf("migration failed for storage path %s: %w", storagePath, err)
		}

		// 累计迁移结果
		allMigrationResults = append(allMigrationResults, results...)
		for _, result := range results {
			if result.Success {
				migratedCount++
			}
		}

		// 继续处理下一个存储路径
	}

	golog.Info("Hard link creation completed successfully (source files will be deleted after database update)",
		"oldPath", oldPath,
		"newPath", newPath,
		"totalImages", len(hashInfo.ImageHashes),
		"totalDocuments", len(hashInfo.DocInfos),
		"totalFiles", totalFiles,
		"migratedCount", migratedCount,
		"strategy", "hard link")

	// 更新目录统计信息
	if !*dryrunFlag {
		if err := updateDirStoreStats(oldPath, newPath, migratedCount); err != nil {
			golog.Warn("Failed to update dirStore stats", "error", err)
			// 不返回错误，因为迁移已经成功
		}
	}

	return allMigrationResults, nil
}

// getListingKey extracts the listing key from the document based on the board type.
func getListingKey(doc bson.M, board string) (string, error) {
	var listingKey string
	var ok bool

	id, _ := doc["_id"].(string) // For logging purposes

	if board == "TRB" || board == "DDF" {
		listingKey, ok = doc["ListingKey"].(string)
		if !ok {
			return "", fmt.Errorf("failed to extract ListingKey for board %s, _id: %s", board, id)
		}
	} else {
		listingKey, ok = doc["ListingId"].(string)
		if !ok {
			return "", fmt.Errorf("failed to extract ListingId for board %s, _id: %s", board, id)
		}
	}
	return listingKey, nil
}

// parseDocFilename parses a document filename and extracts the int32 hash and file extension
// Example: "901700106.pdf" -> (901700106, ".pdf", nil)
// Example: "-1389969486.mp3" -> (-1389969486, ".mp3", nil)
func parseDocFilename(filename string) (int32, string, error) {
	// 分离文件名和扩展名
	ext := filepath.Ext(filename)
	nameWithoutExt := strings.TrimSuffix(filename, ext)

	// 转换为 int32
	hash, err := strconv.ParseInt(nameWithoutExt, 10, 32)
	if err != nil {
		return 0, "", fmt.Errorf("failed to parse filename '%s' as int32: %w", nameWithoutExt, err)
	}

	return int32(hash), ext, nil
}

// processDocLH processes the docLH field and returns a slice of DocInfo
// Handles different array types from MongoDB: []interface{}, primitive.A, []string
func processDocLH(docLH interface{}) ([]DocInfo, error) {
	if docLH == nil {
		return nil, nil
	}

	// Define converter function for string filenames
	stringConverter := func(item interface{}) (string, bool) {
		if filename, ok := item.(string); ok {
			return filename, true
		}
		return "", false
	}

	// Use the generic array processor
	filenames, err := processArrayField(docLH, "docLH", stringConverter)
	if err != nil {
		return nil, err
	}

	// Process all filenames to DocInfo
	var docInfos []DocInfo
	for _, filename := range filenames {
		hash, ext, err := parseDocFilename(filename)
		if err != nil {
			golog.Warn("Failed to parse document filename", "filename", filename, "error", err)
			continue // Skip this filename but continue processing others
		}
		golog.Debug("Parsed document filename", "filename", filename, "hash", hash, "extension", ext)
		docInfos = append(docInfos, DocInfo{Hash: hash, Extension: ext})
	}

	return docInfos, nil
}

// deleteSourceFiles deletes source files after successful database update
// Only deletes files that were successfully migrated (hard links created)
// Uses batch deletion by grouping files by directory and listingKey pattern
func deleteSourceFiles(migrationResults []MigrationResult, stats *MigrationStats) error {
	// Define path extractor for source files
	sourcePathExtractor := func(result MigrationResult) string {
		return result.OldPath
	}

	return batchDeleteFiles(migrationResults, sourcePathExtractor, "source file deletion", stats)
}

// rollbackImageMigration rolls back successfully migrated files by removing hard links
// Since source files are not deleted during migration, we only need to remove the hard links
// Uses batch deletion by grouping files by directory and listingKey pattern
func rollbackImageMigration(migrationResults []MigrationResult) {
	// Define path extractor for destination files (hard links to be removed)
	destinationPathExtractor := func(result MigrationResult) string {
		return result.NewPath
	}

	// Use the generic batch deletion function (no stats update for rollback)
	if err := batchDeleteFiles(migrationResults, destinationPathExtractor, "rollback", nil); err != nil {
		golog.Warn("Rollback encountered errors", "error", err)
	}
}

// printFinalStats prints the final migration statistics
func printFinalStats(stats *MigrationStats, duration time.Duration) {
	fmt.Println("\n" + strings.Repeat("=", 60))
	fmt.Println("MIGRATION SUMMARY")
	fmt.Println(strings.Repeat("=", 60))
	fmt.Printf("Total Processing Time: %v\n", duration)
	fmt.Printf("Total Records Processed: %d\n", stats.TotalProcessed)
	fmt.Printf("Records Already Correct: %d\n", stats.AlreadyCorrect)
	fmt.Printf("Records Needing Migration: %d\n", stats.NeedsMigration)

	if !*dryrunFlag {
		fmt.Printf("File Migration Success: %d\n", stats.MigrationSuccess)
		fmt.Printf("File Migration Failed: %d\n", stats.MigrationFailed)
		fmt.Printf("Database Updates Success: %d\n", stats.DatabaseUpdated)
		fmt.Printf("Database Updates Failed: %d\n", stats.DatabaseFailed)
		fmt.Printf("Files Not Found: %d\n", stats.FilesNotFound)
		fmt.Printf("Files Deleted: %d\n", stats.FilesDeleted)
		fmt.Printf("Properties Added to Queue: %d\n", stats.AddedToQueue)
		fmt.Printf("Photo Fields Unset: %d\n", stats.FieldsUnset)
	} else {
		fmt.Println("Mode: DRY RUN (no actual changes made)")
	}

	fmt.Println(strings.Repeat("=", 60))

	// Log the same information
	golog.Info("Migration completed",
		"duration", duration,
		"totalProcessed", stats.TotalProcessed,
		"alreadyCorrect", stats.AlreadyCorrect,
		"needsMigration", stats.NeedsMigration,
		"migrationSuccess", stats.MigrationSuccess,
		"migrationFailed", stats.MigrationFailed,
		"databaseUpdated", stats.DatabaseUpdated,
		"databaseFailed", stats.DatabaseFailed,
		"filesNotFound", stats.FilesNotFound,
		"filesDeleted", stats.FilesDeleted,
		"addedToQueue", stats.AddedToQueue,
		"fieldsUnset", stats.FieldsUnset,
		"dryrun", *dryrunFlag)
}

// updateDirStoreStats updates directory statistics after file migration
// oldPath: source directory path (e.g., "/1200/abc12")
// newPath: destination directory path (e.g., "/1274/ffc41")
// fileCount: number of files migrated
func updateDirStoreStats(oldPath, newPath string, fileCount int) error {
	if fileCount <= 0 {
		return nil // No files migrated, no stats to update
	}

	// Parse L1 and L2 from old path
	oldL1, oldL2, err := parseL1L2FromPath(oldPath)
	if err != nil {
		return fmt.Errorf("failed to parse old path %s: %w", oldPath, err)
	}

	// Parse L1 and L2 from new path
	newL1, newL2, err := parseL1L2FromPath(newPath)
	if err != nil {
		return fmt.Errorf("failed to parse new path %s: %w", newPath, err)
	}

	// Update statistics: decrease old directory, increase new directory
	// Entity count is 0 because we're moving files, not adding/removing properties
	dirStore.AddDirStats(oldL1, oldL2, -1, -fileCount) // Decrease file count in old directory
	dirStore.AddDirStats(newL1, newL2, 1, fileCount)   // Increase file count in new directory

	golog.Info("Updated dirStore statistics",
		"oldPath", oldPath,
		"newPath", newPath,
		"oldL1", oldL1,
		"oldL2", oldL2,
		"newL1", newL1,
		"newL2", newL2,
		"fileCount", fileCount)

	return nil
}

// parseL1L2FromPath parses L1 and L2 from a path like "/1200/abc12"
func parseL1L2FromPath(path string) (string, string, error) {
	// Remove leading slash and split by "/"
	path = strings.TrimPrefix(path, "/")
	parts := strings.Split(path, "/")

	if len(parts) != 2 {
		return "", "", fmt.Errorf("invalid path format, expected /L1/L2, got: %s", path)
	}

	l1 := parts[0]
	l2 := parts[1]

	if l1 == "" || l2 == "" {
		return "", "", fmt.Errorf("empty L1 or L2 in path: %s", path)
	}

	return l1, l2, nil
}

// handleMissingFiles handles the case when image or document files are missing
// It unsets photo and document related fields and adds the property to download queue
func handleMissingFiles(ctx context.Context, coll *gomongo.MongoCollection, doc bson.M, stats *MigrationStats) error {
	id, ok := doc["_id"].(string)
	if !ok {
		return fmt.Errorf("failed to extract _id from document")
	}

	// Get listing key for queue priority calculation
	listingKey, err := getListingKey(doc, *boardFlag)
	if err != nil {
		return fmt.Errorf("failed to get listing key: %w", err)
	}

	golog.Info("Handling missing files - unsetting photo fields and adding to queue",
		"_id", id,
		"listingKey", listingKey,
		"board", *boardFlag)

	// Step 1: Delete existing image files if they exist
	if err := deleteExistingImages(doc, stats); err != nil {
		golog.Warn("Failed to delete existing images", "error", err, "_id", id)
		// Continue with database update even if file deletion fails
	}

	// Step 2: Unset photo and document related fields in database
	unsetUpdate := bson.M{
		"$unset": bson.M{
			"phoP":  "",
			"phoLH": "",
			"tnLH":  "",
			"docLH": "",
		},
	}

	result, err := coll.UpdateOne(ctx, bson.M{"_id": id}, unsetUpdate)
	if err != nil {
		return fmt.Errorf("failed to unset photo and document fields: %w", err)
	}

	if result.ModifiedCount > 0 {
		stats.FieldsUnset++
		speedMeter.Check("fieldsUnset", 1)
		golog.Info("Successfully unset photo and document fields", "_id", id, "modifiedCount", result.ModifiedCount)
	}

	// Step 3: Calculate priority and add to download queue
	priority := getPriorityForMissingFiles(doc)
	if err := queue.AddToQueue(id, priority, *boardFlag); err != nil {
		return fmt.Errorf("failed to add to download queue: %w", err)
	}

	stats.AddedToQueue++
	speedMeter.Check("addedToQueue", 1)
	golog.Info("Successfully added to download queue",
		"_id", id,
		"listingKey", listingKey,
		"priority", priority,
		"board", *boardFlag)

	return nil
}

// deleteExistingImages deletes existing image and document files for a property
// Uses batch deletion with listingKey_* pattern to delete all related files efficiently
func deleteExistingImages(doc bson.M, stats *MigrationStats) error {
	// Extract phoP path
	phoP, ok := doc["phoP"].(string)
	if !ok || phoP == "" {
		golog.Debug("No phoP found, skipping image deletion")
		return nil
	}

	// Get listing key for batch deletion pattern
	listingKey, err := getListingKey(doc, *boardFlag)
	if err != nil {
		return fmt.Errorf("failed to get listing key for file deletion: %w", err)
	}

	// Get storage paths
	storagePaths, err := levelStore.GetImageDir(*boardFlag)
	if err != nil {
		return fmt.Errorf("failed to get image directories: %w", err)
	}

	deletedCount := 0
	for _, storagePath := range storagePaths {
		dirPath := filepath.Join(storagePath, strings.TrimPrefix(phoP, "/"))

		// 使用批量删除方式，删除所有匹配 listingKey_* 模式的文件
		// 这样可以一次性删除所有相关的图片和文档文件，无论扩展名是什么
		pattern := fmt.Sprintf("%s_*", listingKey)
		batchDeletedCount, err := deleteFilesByPattern(dirPath, pattern)
		if err != nil {
			golog.Warn("Batch deletion encountered errors", "error", err, "dirPath", dirPath, "pattern", pattern)
			// 继续处理，不返回错误，因为部分文件可能已经被删除
		}

		deletedCount += batchDeletedCount

		golog.Info("Batch deleted files for listing",
			"listingKey", listingKey,
			"dirPath", dirPath,
			"pattern", pattern,
			"deletedCount", batchDeletedCount)
	}

	if deletedCount > 0 {
		stats.FilesDeleted += int64(deletedCount)
		speedMeter.Check("filesDeleted", float64(deletedCount))
		golog.Info("Batch deleted existing files",
			"totalDeleted", deletedCount,
			"listingKey", listingKey,
			"phoP", phoP,
			"method", "batch pattern matching")
	}

	return nil
}

// getPriorityForMissingFiles calculates priority for properties with missing files
func getPriorityForMissingFiles(doc bson.M) int {
	// Use high priority for missing files to ensure they get processed quickly
	// This is similar to the NoPhotoPriorityBonus in the priority calculator
	basePriority := 30000 // High priority for missing files

	// Try to use the standard priority calculator if possible
	if priority, err := goresodownload.CalculatePriority(*boardFlag, doc); err == nil {
		// Add bonus to calculated priority
		return priority + basePriority
	}

	// Fallback to base priority
	return basePriority
}
